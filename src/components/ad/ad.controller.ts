import { Controller, Get, HttpStatus, Query, Redirect, Req, Res } from '@nestjs/common';
import { LoggerService } from '../../logger/logger.service';
import { AdService } from './ad.service';
import { adSchema, IAdSchema } from './ad.schema';
import { JoiParamPipe } from './joi-param.pipe';
import { URLParamsHelper } from '../../utils/urlHelper';
import { env } from '../../env/envalidConfig';
import { FastifyReply, FastifyRequest } from 'fastify';

@Controller()
export class AdController {
  constructor(
    private readonly adService: AdService,
    private logger: LoggerService
  ) {
    this.logger.setContext(AdController.name);
  }

  private prepareRedirectUrl(
    baseUrl: string,
    query: Record<string, string | number | boolean>
  ): string {
    const url = new URLParamsHelper(baseUrl, '/');
    for (const [key, val] of Object.entries(query)) {
      url.add(key, val); // add new, if already exists, it will be overwritten
    }
    return url.toString();
  }

  //Przelotka
  @Get('ad.xml')
  @Redirect(env.AD_SERVER_URL, HttpStatus.TEMPORARY_REDIRECT)
  async redirectAd(
    @Query(new JoiParamPipe(adSchema)) requestParams: IAdSchema,
    @Req() req: FastifyRequest,
    @Res() res: FastifyReply
  ) {
    this.logger.log('REQUEST_START', { requestParams });

    const { id5, gdpr, isDebug } = requestParams;

    // Filter out isDebug and undefined values for redirect URL
    const redirectParams = Object.fromEntries(
      Object.entries(requestParams).filter(
        ([key, value]) => key !== 'isDebug' && value !== undefined
      )
    ) as Record<string, string | number | boolean>;

    let redirectUrl = this.prepareRedirectUrl(env.AD_SERVER_URL, redirectParams);

    const herringId = await this.adService.getForAdRequest(id5, gdpr, requestParams);
    if (herringId) {
      const { spratId: aouserid, herringId: ppid } = herringId;
      redirectUrl = this.prepareRedirectUrl(redirectUrl, { aouserid, ppid });
    }

    this.logger.log('REQUEST_END', { herringId, redirectUrl });

    if (isDebug) {
      const requestId = (req as any).requestId;
      if (requestId) {
        const collectedLogs = await LoggerService.getCollectedLogs(requestId);
        res.header('x-tvn-debug-400', collectedLogs.join(','));
      }
    }

    return { url: redirectUrl };
  }
}

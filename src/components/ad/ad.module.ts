import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { AdController } from './ad.controller';
import { LocalCacheModule } from '../../localCache/localCache.module';
import { LoggerModule } from '../../logger/logger.module';
import { ID5DecoderService } from './id5decoder.service';
import { AdService } from './ad.service';
import { RedisModule } from '../../redis/redis.module';
import { IdHelperService } from '../servicesWIthoutModule/idHelper.service';
import { DebugLoggerMiddleware } from '../../middleware/debug-logger.middleware';

@Module({
  imports: [LocalCacheModule, LoggerModule, RedisModule],
  controllers: [AdController],
  providers: [ID5DecoderService, AdService, IdHelperService]
})
export class AdModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(DebugLoggerMiddleware).forRoutes("*");
  }
}

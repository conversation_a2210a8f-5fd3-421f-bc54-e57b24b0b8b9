import { Injectable, NestMiddleware } from '@nestjs/common';
import { FastifyRequest, FastifyReply } from 'fastify';
import { LoggerService } from '../logger/logger.service';
import { randomUUID } from 'crypto';

@Injectable()
export class DebugLoggerMiddleware implements NestMiddleware {
  async use(req: FastifyRequest, res: FastifyReply, next: () => void) {
    const requestId = randomUUID();
    const isDebugMode = this.extractDebugMode(req);

    if (isDebugMode) {
      // Initialize request context for this request
      await LoggerService.initRequestContext(requestId, isDebugMode);

      // Add request ID to the request object for potential use in controllers
      (req as any).requestId = requestId;

      // Manual cleanup after response would be ideal but res.raw is not available in middleware
    }

    next();
  }

  private extractDebugMode(req: FastifyRequest): boolean {
    const query = req.query as any;
    return query?.isDebug === 'true' || query?.isDebug === true;
  }
}

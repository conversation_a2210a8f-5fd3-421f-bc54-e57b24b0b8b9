// import { FastifyRequest, FastifyReply } from 'fastify';
// import { RequestContext, requestContextStorage } from './request-context';

// export function requestContextMiddleware(
//   req: FastifyRequest,
//   res: FastifyReply,
//   next: () => void
// ) {
//   const query = req.query as any;

//   const initialStore: RequestContext = {
//     requestId: query?.requestId,
//     isDebugMode: query?.isDebugMode,
//     collectedLogs: []
//   };

//   requestContextStorage.run(initialStore, () => next());
// }
